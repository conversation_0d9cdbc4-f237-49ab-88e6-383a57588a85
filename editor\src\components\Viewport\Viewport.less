/**
 * 视口组件样式 - 参考ir-engine-dev实现
 */
.viewport-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #2d2d30;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);

  .viewport-top-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    padding: 0 16px;
    background: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    flex-shrink: 0;
    z-index: 20;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .viewport-label {
        color: #cccccc;
        font-size: 12px;
        font-weight: 500;
        margin-right: 8px;
      }

      .transform-tools {
        .ant-btn {
          height: 32px;
          padding: 4px 8px;
          font-size: 12px;
          border-color: #3e3e42;
          background: #3c3c3c;
          color: #cccccc;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            border-color: #007acc;
            background: #505050;
            color: #fff;
          }

          &.ant-btn-primary {
            background: #007acc;
            border-color: #007acc;
            color: #fff;

            &:hover {
              background: #005a9e;
              border-color: #005a9e;
            }
          }
        }
      }

      .ant-btn-group {
        .ant-btn {
          height: 32px;
          padding: 4px 8px;
          font-size: 12px;
          border-color: #3e3e42;
          background: #3c3c3c;
          color: #cccccc;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            border-color: #007acc;
            background: #505050;
            color: #fff;
          }

          &.ant-btn-primary {
            background: #007acc;
            border-color: #007acc;
            color: #fff;

            &:hover {
              background: #005a9e;
              border-color: #005a9e;
            }
          }
        }
      }

      .render-mode-group {
        .ant-radio-button-wrapper {
          height: 32px;
          padding: 4px 12px;
          font-size: 12px;
          border-color: #3e3e42;
          background: #3c3c3c;
          color: #cccccc;
          display: flex;
          align-items: center;

          &:hover {
            border-color: #007acc;
            background: #505050;
            color: #fff;
          }

          &.ant-radio-button-wrapper-checked {
            background: #007acc;
            border-color: #007acc;
            color: #fff;

            &:hover {
              background: #005a9e;
              border-color: #005a9e;
            }
          }
        }
      }
    }

    .toolbar-center {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;

      .viewport-info {
        color: #cccccc;
        font-size: 12px;
        font-weight: 500;
        padding: 4px 8px;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 4px;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;

      .ant-btn-group {
        .ant-btn {
          height: 32px;
          padding: 4px 8px;
          font-size: 12px;
          border-color: #3e3e42;
          background: #3c3c3c;
          color: #cccccc;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            border-color: #007acc;
            background: #505050;
            color: #fff;
          }

          &.ant-btn-primary {
            background: #007acc;
            border-color: #007acc;
            color: #fff;

            &:hover {
              background: #005a9e;
              border-color: #005a9e;
            }
          }
        }
      }
    }
  }

  .viewport-canvas {
    flex: 1;
    width: 100%;
    height: 100%;
    background: #1e1e1e;
    cursor: crosshair;
    outline: none;
    border: none;
    position: relative;
    z-index: 10;
  }

  // 工具提示样式
  .ant-tooltip {
    z-index: 1000;
  }

  .ant-tooltip-inner {
    background: #2d2d30;
    color: #cccccc;
    border: 1px solid #3e3e42;
    font-size: 11px;
  }

  .ant-tooltip-arrow::before {
    background: #2d2d30;
    border: 1px solid #3e3e42;
  }
}
