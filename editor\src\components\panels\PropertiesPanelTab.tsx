/**
 * 属性面板Tab组件 - 完全参考ir-engine-dev实现
 * 参考文件: ir-engine-dev/packages/editor/src/panels/properties/index.tsx
 */
import React, { Suspense } from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import { SettingOutlined } from '@ant-design/icons';
import { ErrorBoundary } from '../common/ErrorBoundary';
import { PanelDragContainer, PanelTitle } from '../common/PanelComponents';
import InspectorPanel from './InspectorPanel';

// 属性容器组件 - 参考ir-engine-dev的PropertiesContainer
const PropertiesContainer = () => {
  return (
    <div style={{
      height: '100%',
      width: '100%',
      position: 'relative',
      background: '#1e1e1e',
      color: '#cccccc'
    }}>
      <Suspense fallback={<div style={{ padding: '20px', color: '#cccccc' }}>Loading properties...</div>}>
        <InspectorPanel />
      </Suspense>
    </div>
  );
};

// 面板标题组件 - 完全参考ir-engine-dev实现
const PropertiesPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <PanelDragContainer dataTestId="properties-panel-tab">
      <PanelTitle>
        <span>{t('editor:properties.title') || 'Properties'}</span>
      </PanelTitle>
    </PanelDragContainer>
  );
};

// 导出TabData格式的面板配置 - 完全按照参考图片修正
export const PropertiesPanelTab: TabData = {
  id: 'properties',
  closable: false,
  title: 'Properties',
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Properties tab</div>}>
      <Suspense fallback={<div>Loading properties...</div>}>
        <PropertiesContainer />
      </Suspense>
    </ErrorBoundary>
  )
};

export default PropertiesPanelTab;
