/**
 * 层级面板Tab组件 - 完全参考ir-engine-dev实现
 * 参考文件: ir-engine-dev/packages/editor/src/panels/hierarchy/index.tsx
 */
import React, { Suspense } from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import { BarsOutlined } from '@ant-design/icons';
import { ErrorBoundary } from '../common/ErrorBoundary';
import { PanelDragContainer, PanelTitle } from '../common/PanelComponents';
import HierarchyPanel from './HierarchyPanel';

// 层级容器组件 - 参考ir-engine-dev的HierarchyContainer
const HierarchyContainer = () => {
  return (
    <div style={{ height: '100%', width: '100%', position: 'relative' }}>
      <Suspense fallback={<div>Loading hierarchy...</div>}>
        <HierarchyPanel />
      </Suspense>
    </div>
  );
};

// 面板标题组件 - 完全参考ir-engine-dev实现
const HierarchyPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <PanelDragContainer dataTestId="hierarchy-panel-tab">
      <PanelTitle>
        <span>{t('editor:hierarchy.title') || 'Hierarchy'}</span>
      </PanelTitle>
    </PanelDragContainer>
  );
};

// 导出TabData格式的面板配置 - 完全按照参考图片修正
export const HierarchyPanelTab: TabData = {
  id: 'hierarchy',
  closable: false,
  title: 'Hierarchy',
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Hierarchy tab</div>}>
      <Suspense fallback={<div>Loading hierarchy...</div>}>
        <HierarchyContainer />
      </Suspense>
    </ErrorBoundary>
  )
};

export default HierarchyPanelTab;
