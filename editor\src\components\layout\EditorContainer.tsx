/**
 * 编辑器容器组件 - 完全参考ir-engine-dev实现
 * 参考文件: ir-engine-dev/packages/editor/src/components/EditorContainer.tsx
 */
import React, { useState, useEffect, useRef, useCallback, useMemo, Suspense } from 'react';
import { Button, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { DockLayout, LayoutData, TabData, DockMode } from 'rc-dock';
import 'rc-dock/dist/rc-dock.css';

import { useAppDispatch, useAppSelector } from '../../store';
import { ErrorBoundary } from '../common/ErrorBoundary';
import Toolbar from '../toolbar/Toolbar';
import LeftToolbar from '../toolbar/LeftToolbar';

// 引入面板Tab定义 - 完全按照参考图片布局
import { ViewportPanelTab } from '../panels/ViewportPanelTab';
import { HierarchyPanelTab } from '../panels/HierarchyPanelTab';
import { AssetsPanelTab } from '../panels/AssetsPanelTab';
import { PropertiesPanelTab } from '../panels/PropertiesPanelTab';
import { MaterialsPanelTab } from '../panels/MaterialsPanelTab';
import { ScenesPanelTab } from '../panels/ScenesPanelTab';
import { FilesPanelTab } from '../panels/FilesPanelTab';
import { VisualScriptingPanelTab } from '../panels/VisualScriptingPanelTab';

// 引入样式
import './EditorContainer.css';

// 活动下方面板类型
type ActiveLowerPanel = 'propertiesPanel' | 'inspectorPanel';

// EditorContainer组件的Props
interface EditorContainerProps {
  projectId?: string;
  sceneId?: string;
}

// DockContainer组件 - 参考ir-engine-dev实现
export const DockContainer = ({ children, id = 'editor-dock', dividerAlpha = 0 }) => {
  const dockContainerStyles = {
    '--dividerAlpha': dividerAlpha
  };

  return (
    <div id={id} className="dock-container" style={dockContainerStyles as React.CSSProperties}>
      {children}
    </div>
  );
};

// 默认布局配置 - 完全按照参考图片重新设计
const defaultLayout = (flags: {
  visualScriptPanelEnabled: boolean;
  activeLowerPanel: ActiveLowerPanel;
}): LayoutData => {
  // 底部面板标签页 - 按照参考图片包含Files, Assets, Scenes
  const bottomTabs = [FilesPanelTab, AssetsPanelTab, ScenesPanelTab];
  if (flags.visualScriptPanelEnabled) {
    bottomTabs.push(VisualScriptingPanelTab);
  }

  return {
    dockbox: {
      mode: 'horizontal' as DockMode,
      children: [
        // 左侧：主视口区域（占大部分空间）
        {
          mode: 'vertical' as DockMode,
          size: 7,
          children: [
            // 上方：主视口
            {
              size: 7,
              tabs: [ViewportPanelTab]
            },
            // 下方：底部面板（Files, Assets, Scenes）
            {
              size: 3,
              tabs: bottomTabs
            }
          ]
        },
        // 右侧面板区域（占较小空间）
        {
          mode: 'vertical' as DockMode,
          size: 3,
          children: [
            // 上方：Hierarchy 和 Material Library
            {
              size: 1,
              tabs: [HierarchyPanelTab, MaterialsPanelTab]
            },
            // 下方：Properties
            {
              size: 1,
              tabs: [PropertiesPanelTab]
            }
          ]
        }
      ]
    }
  };
};

const EditorContainer: React.FC<EditorContainerProps> = ({ projectId, sceneId }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const dockPanelRef = useRef<DockLayout>(null);

  // 状态管理 - 与ir-engine-dev一致
  const [visualScriptPanelEnabled] = useState(false);
  const [activeLowerPanel] = useState<ActiveLowerPanel>('propertiesPanel');
  const [uiEnabled] = useState(true);
  const [scenePath] = useState<string | null>(sceneId || null);

  // 记忆化默认布局
  const memoizedDefaultLayout = useMemo(() => {
    return defaultLayout({
      visualScriptPanelEnabled,
      activeLowerPanel
    });
  }, [visualScriptPanelEnabled, activeLowerPanel]);

  return (
    <ErrorBoundary fallback={<div>编辑器容器加载失败</div>}>
      <main className="pointer-events-auto">
        <div
          id="editor-container"
          className="flex flex-col editor-container"
          style={scenePath ? { background: 'transparent' } : {}}
        >
          {uiEnabled && (
            <>
              <Toolbar projectId={projectId} sceneId={sceneId} />
              <div className="flex overflow-hidden" style={{ height: 'calc(100vh - 40px)' }}>
                <LeftToolbar />
                <div style={{ flex: 1, position: 'relative' }}>
                  <DockContainer>
                    <DockLayout
                      ref={dockPanelRef}
                      defaultLayout={memoizedDefaultLayout}
                      style={{
                        position: 'absolute',
                        left: 0,
                        top: 0,
                        right: 0,
                        bottom: 0
                      }}
                    />
                  </DockContainer>
                </div>
              </div>
            </>
          )}
        </div>
      </main>
    </ErrorBoundary>
  );
};

export default EditorContainer;
