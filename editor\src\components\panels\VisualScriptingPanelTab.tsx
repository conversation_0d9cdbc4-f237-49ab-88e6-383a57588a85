/**
 * Visual Scripting面板标签页 - 参考ir-engine-dev实现
 */
import React, { useState, Suspense } from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import {
  CodeOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SaveOutlined,
  FolderOpenOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { Button, Space, List, Card, Empty, Tooltip, Divider } from 'antd';
import { ErrorBoundary } from '../common/ErrorBoundary';
import { StandardPanelTitle, PanelLoading, PanelError } from '../common/PanelComponents';

const VisualScriptingPanel: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [scripts, setScripts] = useState([
    {
      id: '1',
      name: 'Player Controller',
      description: '玩家控制脚本',
      lastModified: '2024-01-01',
      status: 'active'
    },
    {
      id: '2',
      name: 'Camera Follow',
      description: '相机跟随脚本',
      lastModified: '2024-01-01',
      status: 'inactive'
    }
  ]);

  const handleRunScript = () => {
    setIsRunning(!isRunning);
  };

  const handleNewScript = () => {
    const newScript = {
      id: Date.now().toString(),
      name: `New Script ${scripts.length + 1}`,
      description: '新建脚本',
      lastModified: new Date().toISOString().split('T')[0],
      status: 'inactive'
    };
    setScripts([...scripts, newScript]);
  };

  const handleDeleteScript = (id: string) => {
    setScripts(scripts.filter(script => script.id !== id));
  };

  return (
    <div style={{ padding: '8px', height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 工具栏 */}
      <Space style={{ marginBottom: '8px' }}>
        <Button
          type="primary"
          icon={isRunning ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
          onClick={handleRunScript}
          size="small"
        >
          {isRunning ? '停止' : '运行'}
        </Button>
        <Button icon={<SaveOutlined />} size="small">
          保存
        </Button>
        <Button icon={<FolderOpenOutlined />} size="small">
          打开
        </Button>
        <Divider type="vertical" />
        <Button icon={<PlusOutlined />} onClick={handleNewScript} size="small">
          新建脚本
        </Button>
      </Space>

      {/* 脚本列表 */}
      <div style={{ flex: 1, overflow: 'auto' }}>
        {scripts.length > 0 ? (
          <List
            dataSource={scripts}
            renderItem={(script) => (
              <List.Item>
                <Card
                  size="small"
                  hoverable
                  style={{ width: '100%' }}
                  actions={[
                    <Tooltip key="edit" title="编辑">
                      <Button type="link" icon={<CodeOutlined />} size="small">
                        编辑
                      </Button>
                    </Tooltip>,
                    <Tooltip key="delete" title="删除">
                      <Button 
                        type="link" 
                        danger 
                        icon={<DeleteOutlined />} 
                        size="small"
                        onClick={() => handleDeleteScript(script.id)}
                      >
                        删除
                      </Button>
                    </Tooltip>
                  ]}
                >
                  <Card.Meta
                    avatar={<CodeOutlined />}
                    title={
                      <Space>
                        {script.name}
                        <span 
                          style={{ 
                            fontSize: '10px',
                            padding: '2px 6px',
                            borderRadius: '4px',
                            background: script.status === 'active' ? '#52c41a' : '#d9d9d9',
                            color: script.status === 'active' ? '#fff' : '#666'
                          }}
                        >
                          {script.status === 'active' ? '活跃' : '未激活'}
                        </span>
                      </Space>
                    }
                    description={
                      <div>
                        <div>{script.description}</div>
                        <div style={{ fontSize: '11px', color: '#999', marginTop: '4px' }}>
                          最后修改: {script.lastModified}
                        </div>
                      </div>
                    }
                  />
                </Card>
              </List.Item>
            )}
          />
        ) : (
          <Empty
            description="暂无可视化脚本"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button type="primary" icon={<PlusOutlined />} onClick={handleNewScript}>
              创建脚本
            </Button>
          </Empty>
        )}
      </div>

      {/* 状态栏 */}
      <div style={{ 
        borderTop: '1px solid #3e3e42', 
        padding: '4px 0', 
        fontSize: '11px', 
        color: '#999',
        marginTop: '8px'
      }}>
        状态: {isRunning ? '运行中...' : '已停止'} | 脚本数量: {scripts.length}
      </div>
    </div>
  );
};

// 面板标题组件 - 使用标准化组件
const VisualScriptingPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <StandardPanelTitle
      icon={<CodeOutlined />}
      title={t('editor.visualScripting') || 'Visual Scripting'}
      tooltip={t('editor.visualScripting.tooltip') || 'Visual scripting editor'}
    />
  );
};

// 导出标签页配置 - 修复内容渲染问题
export const VisualScriptingPanelTab: TabData = {
  id: 'visual-scripting',
  title: 'Visual Scripting',
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Visual Scripting tab</div>}>
      <Suspense fallback={<div>Loading visual scripting...</div>}>
        <VisualScriptingPanel />
      </Suspense>
    </ErrorBoundary>
  ),
  closable: true
};

export default VisualScriptingPanel;
export { VisualScriptingPanel };
