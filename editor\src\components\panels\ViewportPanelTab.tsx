/**
 * 视口面板Tab组件 - 完全参考ir-engine-dev实现
 * 参考文件: ir-engine-dev/packages/editor/src/panels/viewport/index.tsx
 */
import React, { Suspense } from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import { EyeOutlined } from '@ant-design/icons';
import { ErrorBoundary } from '../common/ErrorBoundary';
import { PanelDragContainer, PanelTitle } from '../common/PanelComponents';
import Viewport from '../Viewport/index';

// 视口容器组件 - 参考ir-engine-dev的ViewportContainer
const ViewportContainer = () => {
  return (
    <div style={{ height: '100%', width: '100%', position: 'relative' }}>
      <Suspense fallback={<div>Loading viewport...</div>}>
        <Viewport />
      </Suspense>
    </div>
  );
};

// 面板标题组件 - 完全参考ir-engine-dev实现
export const ViewportPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <PanelDragContainer>
      <PanelTitle>{t('editor:viewport.title') || 'Viewport'}</PanelTitle>
    </PanelDragContainer>
  );
};

// 导出TabData格式的面板配置 - 完全按照参考图片修正
export const ViewportPanelTab: TabData = {
  id: 'viewport',
  closable: false,
  title: 'View port',
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Viewport tab</div>}>
      <Suspense fallback={<div>Loading viewport...</div>}>
        <ViewportContainer />
      </Suspense>
    </ErrorBoundary>
  )
};

export default ViewportPanelTab;
