/**
 * 编辑器工具栏组件 - 完全参考ir-engine-dev实现
 * 参考文件: ir-engine-dev/packages/editor/src/components/toolbar/Toolbar.tsx
 */
import React, { useState, Fragment } from 'react';
import { Button, Dropdown, Space, Tooltip } from 'antd';
import {
  AppstoreOutlined,
  DownOutlined,
  FileOutlined,
  UserOutlined,
  CloudUploadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

interface ToolbarProps {
  projectId?: string;
  sceneId?: string;
  onPublish?: () => void;
}

const Toolbar: React.FC<ToolbarProps> = ({
  projectId = 'default-project',
  sceneId = 'default-scene',
  onPublish
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [isPublishing, setIsPublishing] = useState(false);

  // 返回项目列表
  const handleBackToDashboard = () => {
    navigate('/projects');
  };

  // 发布场景
  const handlePublish = async () => {
    if (onPublish) {
      setIsPublishing(true);
      try {
        await onPublish();
      } finally {
        setIsPublishing(false);
      }
    }
  };

  // 主菜单项 - 参考ir-engine-dev
  const mainMenuItems = [
    {
      key: 'new-scene',
      label: t('editor:menubar.newScene') || 'New Scene'
    },
    {
      key: 'save-scene',
      label: t('editor:menubar.saveScene') || 'Save Scene'
    },
    {
      key: 'save-as',
      label: t('editor:menubar.saveAs') || 'Save As'
    },
    {
      key: 'import-settings',
      label: t('editor:menubar.importSettings') || 'Import Settings'
    },
    {
      key: 'import-asset',
      label: t('editor:menubar.importAsset') || 'Import Asset'
    },
    {
      key: 'export-lookdev',
      label: t('editor:menubar.exportLookdev') || 'Export Lookdev'
    },
    {
      key: 'documentation',
      label: t('editor:menubar.documentation') || 'Documentation'
    },
    {
      key: 'quit',
      label: t('editor:menubar.quit') || 'Quit'
    }
  ];

  // 项目名称和场景名称处理 - 按照参考图片显示
  const projectName = 'Electronics Demo Project';
  const sceneName = 'Electronics Store';

  return (
    <div
      className="flex h-10 items-center justify-between px-4 py-0.5"
      style={{
        background: '#1e1e1e',
        borderBottom: '1px solid #3e3e42'
      }}
    >
      {/* 左侧：Logo和主菜单 - 参考ir-engine-dev */}
      <div className="flex items-center">
        <div className="cursor-pointer" onClick={handleBackToDashboard}>
          <AppstoreOutlined style={{ color: '#ffffff', fontSize: '16px' }} />
        </div>
        <Dropdown menu={{ items: mainMenuItems }} placement="bottomLeft">
          <button
            style={{
              background: 'transparent',
              border: 'none',
              color: '#ffffff',
              marginLeft: '8px'
            }}
          >
            <DownOutlined />
          </button>
        </Dropdown>
      </div>

      {/* 中间：面包屑导航 - 参考ir-engine-dev */}
      <div className="flex items-center gap-2.5">
        <FileOutlined style={{ color: '#cccccc' }} />
        {projectName.split('/').map((part, index) => (
          <Fragment key={index}>
            <span style={{ color: '#cccccc' }}>{part}</span>
            <span style={{ color: '#cccccc' }}>{' / '}</span>
          </Fragment>
        ))}
        <span style={{ color: '#ffffff' }}>{sceneName}</span>
      </div>

      {/* 右侧：用户头像和发布按钮 - 参考ir-engine-dev */}
      <div className="flex items-center justify-center gap-2">
        <div
          style={{
            width: '32px',
            height: '32px',
            borderRadius: '50%',
            background: '#007acc',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <UserOutlined style={{ color: '#ffffff' }} />
        </div>

        {sceneId && (
          <div className="p-2">
            <Button
              disabled={false}
              onClick={handlePublish}
              loading={isPublishing}
              style={{
                borderRadius: '8px',
                padding: '4px 12px',
                fontSize: '14px',
                background: '#007acc',
                borderColor: '#007acc',
                color: '#ffffff'
              }}
              type="primary"
            >
              <CloudUploadOutlined />
              {t('editor:toolbar.lbl-publish') || 'Publish'}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Toolbar;