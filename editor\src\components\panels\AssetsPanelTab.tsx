/**
 * 资源面板Tab组件 - 完全参考ir-engine-dev实现
 * 参考文件: ir-engine-dev/packages/editor/src/panels/assets/index.tsx
 */
import React, { Suspense } from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import { FolderOutlined } from '@ant-design/icons';
import { ErrorBoundary } from '../common/ErrorBoundary';
import { PanelDragContainer, PanelTitle } from '../common/PanelComponents';
import AssetsPanel from './AssetsPanel';

// 资源容器组件 - 参考ir-engine-dev的AssetsContainer
const AssetsContainer = () => {
  return (
    <div style={{
      height: '100%',
      width: '100%',
      position: 'relative',
      background: '#1e1e1e',
      color: '#cccccc'
    }}>
      <Suspense fallback={<div style={{ padding: '20px', color: '#cccccc' }}>Loading assets...</div>}>
        <AssetsPanel />
      </Suspense>
    </div>
  );
};

// 面板标题组件 - 完全参考ir-engine-dev实现
const AssetsPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <div>
      <PanelDragContainer dataTestId="assets-panel-tab">
        <PanelTitle>
          <span>{t('editor:tabs.scene-assets') || 'Scene Assets'}</span>
        </PanelTitle>
      </PanelDragContainer>
    </div>
  );
};

// 导出TabData格式的面板配置 - 完全按照参考图片修正
export const AssetsPanelTab: TabData = {
  id: 'assets',
  closable: false,
  title: 'Assets',
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Assets tab</div>}>
      <Suspense fallback={<div>Loading assets...</div>}>
        <AssetsContainer />
      </Suspense>
    </ErrorBoundary>
  )
};

export default AssetsPanelTab;
