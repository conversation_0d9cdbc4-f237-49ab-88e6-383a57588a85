/**
 * 材质面板Tab组件 - 完全参考ir-engine-dev实现
 * 参考文件: ir-engine-dev/packages/editor/src/panels/materials/index.tsx
 */
import React, { Suspense } from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import { AppstoreOutlined } from '@ant-design/icons';
import { ErrorBoundary } from '../common/ErrorBoundary';
import { PanelDragContainer, PanelTitle } from '../common/PanelComponents';
import MaterialLibraryPanel from './MaterialLibraryPanel';

// 材质容器组件 - 参考ir-engine-dev的MaterialsContainer
const MaterialsContainer = () => {
  return (
    <div style={{ height: '100%', width: '100%', position: 'relative' }}>
      <Suspense fallback={<div>Loading materials...</div>}>
        <MaterialLibraryPanel />
      </Suspense>
    </div>
  );
};

// 面板标题组件 - 完全参考ir-engine-dev实现
const MaterialsPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <PanelDragContainer dataTestId="materials-panel-tab">
      <PanelTitle>
        <span>{t('editor:materials.title') || 'Materials'}</span>
      </PanelTitle>
    </PanelDragContainer>
  );
};

// 导出TabData格式的面板配置 - 完全按照参考图片修正
export const MaterialsPanelTab: TabData = {
  id: 'materials',
  closable: false,
  title: 'Material Library',
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Materials tab</div>}>
      <Suspense fallback={<div>Loading materials...</div>}>
        <MaterialsContainer />
      </Suspense>
    </ErrorBoundary>
  )
};

export default MaterialsPanelTab;
