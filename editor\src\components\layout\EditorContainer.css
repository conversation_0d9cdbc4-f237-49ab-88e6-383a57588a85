/**
 * 编辑器容器样式 - 完全参考ir-engine-dev实现
 * 参考文件: ir-engine-dev/packages/editor/src/components/EditorContainer.css
 */

/* 编辑器容器主样式 - 根据参考图片调整 */
.editor-container {
  height: 100vh;
  width: 100vw;
  background: #2d2d30;
  color: #cccccc;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 主要内容区域样式 */
.editor-main-content {
  flex: 1;
  display: flex;
  height: calc(100vh - 40px);
}

/* rc-dock容器样式 */
.dock-container {
  flex: 1;
  position: relative;
  background: transparent;
}

/* rc-dock面板样式覆盖 - 完全参考ir-engine-dev */
.dock-top .dock-bar {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: #2d2d30 !important;
  color: #cccccc;
}

.dock-panel {
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: #1e1e1e;
  color: #cccccc;
}

.dock-panel-max-btn::before {
  border-color: #cccccc !important;
}

.dock.dock-top {
  background: #2d2d30;
}

.dock-tab {
  background: #2d2d30;
  color: #cccccc;
  border: none;
  border-bottom: 2px solid transparent;
}

.dock-tab:hover {
  background: #3e3e42;
}

.dock-tab.dock-tab-active {
  background: #1e1e1e;
  color: #ffffff;
  border-bottom-color: #007acc;
}

.dock-nav-list {
  justify-content: center;
  align-items: center;
}

.dock-top .dock-bar {
  border-bottom: none !important;
}

.dock-tab {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  background: transparent !important;
  margin: 0;
  border-bottom: none !important;
  color: var(--text-secondary, #cccccc) !important;
}

.dock-tab.dock-tab-active {
  background: var(--ui-select-background, #007acc) !important;
  color: var(--text-primary, #ffffff) !important;
}

.dock-tab:not(.dock-tab-active):hover {
  background: var(--bg-surface-input, #3e3e42) !important;
}

.dock-tab > div {
  padding: 0 !important;
}

.dock-ink-bar {
  display: none;
}

.dock-tab-close-btn{
  display: none;
}

.dock-bar.drag-initiator{
  padding: 0;
}

/* 面板内容样式 */
.dock-panel-content {
  background: #1e1e1e !important;
  color: #ffffff !important;
  padding: 0 !important;
}

/* 面板标题样式 */
.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  padding: 8px 12px;
}

.panel-title-icon {
  width: 16px;
  height: 16px;
  color: #cccccc;
}

/* 工具栏样式 */
.editor-toolbar {
  height: 40px;
  background: #1e1e1e;
  border-bottom: 1px solid #3e3e42;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 1000;
}

/* 移除数字输入框的箭头 - 与ir-engine-dev一致 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] {
  -moz-appearance: textfield;
}

/* 面板内容区域样式 */
.panel-content {
  height: 100%;
  overflow: auto;
  padding: 8px;
  background: #1e1e1e;
  color: #cccccc;
}

.panel-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.panel-content::-webkit-scrollbar-track {
  background: transparent;
}

.panel-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Ant Design 深色主题覆盖 */
.ant-tabs {
  color: #cccccc;
}

.ant-tabs-tab {
  color: #cccccc !important;
  background: transparent !important;
}

.ant-tabs-tab:hover {
  color: #ffffff !important;
}

.ant-tabs-tab-active {
  color: #007acc !important;
}

.ant-tabs-content-holder {
  background: #1e1e1e;
  color: #cccccc;
}

.ant-form-item-label > label {
  color: #cccccc !important;
}

.ant-input {
  background: #2d2d30 !important;
  border-color: #3e3e42 !important;
  color: #cccccc !important;
}

.ant-input:hover {
  border-color: #007acc !important;
}

.ant-input:focus {
  border-color: #007acc !important;
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2) !important;
}

.ant-input-number {
  background: #2d2d30 !important;
  border-color: #3e3e42 !important;
  color: #cccccc !important;
}

.ant-input-number:hover {
  border-color: #007acc !important;
}

.ant-input-number:focus {
  border-color: #007acc !important;
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2) !important;
}

.ant-switch {
  background: #3e3e42 !important;
}

.ant-switch-checked {
  background: #007acc !important;
}

.ant-select-selector {
  background: #2d2d30 !important;
  border-color: #3e3e42 !important;
  color: #cccccc !important;
}

.ant-select-selector:hover {
  border-color: #007acc !important;
}

.ant-tree {
  background: #1e1e1e !important;
  color: #cccccc !important;
}

.ant-tree-node-content-wrapper {
  color: #cccccc !important;
}

.ant-tree-node-content-wrapper:hover {
  background: #3e3e42 !important;
}

.ant-tree-node-selected {
  background: #007acc !important;
  color: #ffffff !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1e1e1e;
}

::-webkit-scrollbar-thumb {
  background: #3e3e42;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #007acc;
}

::-webkit-scrollbar-corner {
  background: #1e1e1e;
}
