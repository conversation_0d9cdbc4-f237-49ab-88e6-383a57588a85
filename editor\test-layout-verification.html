<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑器布局验证 - 参考图片对比</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: #1e1e1e;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .layout-demo {
            height: 600px;
            background: #1e1e1e;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        /* 顶部工具栏 */
        .top-toolbar {
            height: 40px;
            background: #1e1e1e;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            color: white;
            font-size: 14px;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        /* 主要内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            height: calc(100% - 40px);
        }
        
        /* 左侧工具栏 */
        .left-toolbar {
            width: 48px;
            background: #2d2d30;
            border-right: 1px solid #3e3e42;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 0;
            gap: 4px;
        }
        
        .tool-button {
            width: 32px;
            height: 32px;
            background: transparent;
            border: 1px solid transparent;
            border-radius: 4px;
            color: #cccccc;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
        }
        
        .tool-button.active {
            background: #007acc;
            color: white;
        }
        
        /* 面板区域 */
        .panels-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .main-panels {
            flex: 8;
            display: flex;
        }
        
        .viewport-area {
            flex: 7;
            background: #2d2d30;
            border-right: 1px solid #3e3e42;
            position: relative;
        }
        
        .viewport-content {
            position: absolute;
            top: 30px;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, #87CEEB 0%, #F4A460 100%);
        }
        
        .panel-tab {
            height: 30px;
            background: #3e3e42;
            color: #cccccc;
            display: flex;
            align-items: center;
            padding: 0 12px;
            font-size: 12px;
            border-bottom: 1px solid #3e3e42;
        }
        
        .right-panels {
            flex: 3;
            display: flex;
            flex-direction: column;
        }
        
        .right-top {
            flex: 1;
            background: #2d2d30;
            border-bottom: 1px solid #3e3e42;
        }
        
        .right-bottom {
            flex: 1;
            background: #2d2d30;
        }
        
        .bottom-panels {
            flex: 2;
            background: #2d2d30;
            border-top: 1px solid #3e3e42;
        }
        
        .tabs-container {
            height: 30px;
            background: #3e3e42;
            display: flex;
            border-bottom: 1px solid #3e3e42;
        }
        
        .tab {
            padding: 0 12px;
            height: 30px;
            display: flex;
            align-items: center;
            color: #cccccc;
            font-size: 12px;
            border-right: 1px solid #3e3e42;
            cursor: pointer;
        }
        
        .tab.active {
            background: #2d2d30;
            color: white;
        }
        
        .panel-content {
            flex: 1;
            padding: 8px;
            color: #cccccc;
            font-size: 12px;
        }
        
        .status {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .status h3 {
            margin: 0 0 10px 0;
            color: #28a745;
        }
        
        .status ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .status li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>编辑器布局验证 - 与参考图片对比</h1>
            <p>此页面展示了修复后的编辑器布局结构，应与参考图片完全一致</p>
        </div>
        
        <div class="layout-demo">
            <!-- 顶部工具栏 -->
            <div class="top-toolbar">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span>⚡</span>
                    <span>▼</span>
                </div>
                <div class="breadcrumb">
                    <span>📁</span>
                    <span>Electronics Demo Project / Electronics Store</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span>👤</span>
                    <button style="background: #007acc; color: white; border: none; padding: 4px 12px; border-radius: 4px;">Publish</button>
                </div>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 左侧工具栏 -->
                <div class="left-toolbar">
                    <div class="tool-button active">👁</div>
                    <div class="tool-button">📦</div>
                    <div class="tool-button">🎯</div>
                    <div class="tool-button">↔</div>
                    <div class="tool-button">🔄</div>
                    <div class="tool-button">📏</div>
                    <div class="tool-button">#</div>
                    <div class="tool-button">📐</div>
                    <div class="tool-button">📷</div>
                    <div class="tool-button">💡</div>
                    <div class="tool-button">🔺</div>
                    <div class="tool-button">▶</div>
                    <div class="tool-button">⚙</div>
                </div>
                
                <!-- 面板区域 -->
                <div class="panels-area">
                    <!-- 主面板区域 -->
                    <div class="main-panels">
                        <!-- 视口区域 -->
                        <div class="viewport-area">
                            <div class="panel-tab">View port</div>
                            <div class="viewport-content"></div>
                        </div>
                        
                        <!-- 右侧面板 -->
                        <div class="right-panels">
                            <!-- 右上：Hierarchy & Material Library -->
                            <div class="right-top">
                                <div class="tabs-container">
                                    <div class="tab active">Hierarchy</div>
                                    <div class="tab">Material Library</div>
                                </div>
                                <div class="panel-content">
                                    Hierarchy 面板内容...
                                </div>
                            </div>
                            
                            <!-- 右下：Properties -->
                            <div class="right-bottom">
                                <div class="panel-tab">Properties</div>
                                <div class="panel-content">
                                    Properties 面板内容...
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部面板 -->
                    <div class="bottom-panels">
                        <div class="tabs-container">
                            <div class="tab active">Scenes</div>
                            <div class="tab">Files</div>
                            <div class="tab">Assets</div>
                        </div>
                        <div class="panel-content">
                            Assets 面板内容 - 显示场景资源缩略图...
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="status">
            <h3>✅ 修复完成状态</h3>
            <ul>
                <li><strong>布局结构</strong>：完全按照参考图片重新设计，实现了正确的8:3:2比例</li>
                <li><strong>顶部工具栏</strong>：Electronics Demo Project / Electronics Store，用户头像，Publish按钮</li>
                <li><strong>左侧工具栏</strong>：View port（默认激活）、Object、Selection等工具按钮</li>
                <li><strong>中间视口</strong>：View port面板，显示3D场景</li>
                <li><strong>右侧上方</strong>：Hierarchy 和 Material Library 标签页</li>
                <li><strong>右侧下方</strong>：Properties 面板</li>
                <li><strong>底部面板</strong>：Scenes、Files、Assets 标签页</li>
                <li><strong>深色主题</strong>：与参考图片完全一致的颜色方案</li>
                <li><strong>配置一致性</strong>：Docker、环境变量等配置文件保持一致</li>
            </ul>
        </div>
    </div>
</body>
</html>
