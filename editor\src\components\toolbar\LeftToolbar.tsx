/**
 * 编辑器左侧工具栏组件
 * 提供常用的编辑工具（选择、移动、旋转、缩放等）
 */
import React, { useState } from 'react';
import { Tooltip } from 'antd';
import {
  SelectOutlined,
  ArrowsAltOutlined,
  RotateRightOutlined,
  ColumnWidthOutlined,
  BorderOutlined,
  EyeOutlined,
  SettingOutlined,
  AppstoreOutlined,
  CameraOutlined,
  BulbOutlined,
  BoxPlotOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setTransformMode,
  setShowGrid,
  setShowAxes,
  setIsPlaying,
  TransformMode
} from '../../store/editor/editorSlice';
import './LeftToolbar.less';

type ToolType = 'select' | 'object' | 'selection' | 'move' | 'rotate' | 'scale' | 'grid' | 'axes' | 'camera' | 'light' | 'primitive' | 'play' | 'settings';

interface LeftToolbarProps {
  onToolChange?: (tool: ToolType) => void;
}

const LeftToolbar: React.FC<LeftToolbarProps> = ({ onToolChange }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [activeTool, setActiveTool] = useState<ToolType>('select');

  const {
    transformMode,
    showGrid,
    showAxes,
    isPlaying
  } = useAppSelector((state) => state.editor);

  const tools = [
    {
      key: 'select' as ToolType,
      icon: <EyeOutlined />,
      title: 'View port',
      shortcut: 'Q',
      group: 'main',
      active: true // 默认激活View port工具
    },
    {
      key: 'object' as ToolType,
      icon: <AppstoreOutlined />,
      title: 'Object',
      shortcut: 'O',
      group: 'main'
    },
    {
      key: 'selection' as ToolType,
      icon: <SelectOutlined />,
      title: 'Selection',
      shortcut: 'S',
      group: 'main'
    },
    {
      key: 'move' as ToolType,
      icon: <ArrowsAltOutlined />,
      title: t('editor.translateTool') || 'Translate',
      shortcut: 'W',
      group: 'transform',
      active: transformMode === TransformMode.TRANSLATE
    },
    {
      key: 'rotate' as ToolType,
      icon: <RotateRightOutlined />,
      title: t('editor.rotateTool') || 'Rotate',
      shortcut: 'E',
      group: 'transform',
      active: transformMode === TransformMode.ROTATE
    },
    {
      key: 'scale' as ToolType,
      icon: <ColumnWidthOutlined />,
      title: t('editor.scaleTool') || 'Scale',
      shortcut: 'R',
      group: 'transform',
      active: transformMode === TransformMode.SCALE
    },
    {
      key: 'grid' as ToolType,
      icon: <BorderOutlined />,
      title: t('editor.grid') || 'Grid',
      shortcut: 'G',
      group: 'view',
      active: showGrid
    },
    {
      key: 'axes' as ToolType,
      icon: <EyeOutlined />,
      title: t('editor.axes') || 'Axes',
      shortcut: 'X',
      group: 'view',
      active: showAxes
    },
    {
      key: 'camera' as ToolType,
      icon: <CameraOutlined />,
      title: t('editor.camera') || 'Camera',
      shortcut: 'C',
      group: 'create'
    },
    {
      key: 'light' as ToolType,
      icon: <BulbOutlined />,
      title: t('editor.light') || 'Light',
      shortcut: 'L',
      group: 'create'
    },
    {
      key: 'primitive' as ToolType,
      icon: <BoxPlotOutlined />,
      title: t('editor.primitive') || 'Primitive',
      shortcut: 'P',
      group: 'create'
    },
    {
      key: 'play' as ToolType,
      icon: isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />,
      title: isPlaying ? (t('editor.pause') || 'Pause') : (t('editor.play') || 'Play'),
      shortcut: 'Space',
      group: 'playback',
      active: isPlaying
    },
    {
      key: 'settings' as ToolType,
      icon: <SettingOutlined />,
      title: t('editor.toolSettings') || 'Settings',
      shortcut: 'S',
      group: 'settings'
    }
  ];

  const handleToolClick = (tool: ToolType) => {
    setActiveTool(tool);
    onToolChange?.(tool);

    // 处理工具状态变化
    switch (tool) {
      case 'move':
        dispatch(setTransformMode(TransformMode.TRANSLATE));
        break;
      case 'rotate':
        dispatch(setTransformMode(TransformMode.ROTATE));
        break;
      case 'scale':
        dispatch(setTransformMode(TransformMode.SCALE));
        break;
      case 'grid':
        dispatch(setShowGrid(!showGrid));
        break;
      case 'axes':
        dispatch(setShowAxes(!showAxes));
        break;
      case 'play':
        dispatch(setIsPlaying(!isPlaying));
        break;
    }
  };

  // 按组分组工具
  const groupedTools = tools.reduce((groups, tool) => {
    if (!groups[tool.group]) {
      groups[tool.group] = [];
    }
    groups[tool.group].push(tool);
    return groups;
  }, {} as Record<string, typeof tools>);

  return (
    <div className="left-toolbar">
      <div className="toolbar-tools">
        {/* 主要工具 */}
        {groupedTools.main?.map((tool) => (
          <Tooltip
            key={tool.key}
            title={`${tool.title} (${tool.shortcut})`}
            placement="right"
          >
            <div
              className={`tool-button ${tool.active || activeTool === tool.key ? 'active' : ''}`}
              onClick={() => handleToolClick(tool.key)}
            >
              {tool.icon}
            </div>
          </Tooltip>
        ))}

        {/* 分隔线 */}
        <div className="toolbar-divider" />

        {/* 变换工具 */}
        {groupedTools.transform?.map((tool) => (
          <Tooltip
            key={tool.key}
            title={`${tool.title} (${tool.shortcut})`}
            placement="right"
          >
            <div
              className={`tool-button ${tool.active || activeTool === tool.key ? 'active' : ''}`}
              onClick={() => handleToolClick(tool.key)}
            >
              {tool.icon}
            </div>
          </Tooltip>
        ))}

        {/* 分隔线 */}
        <div className="toolbar-divider" />

        {/* 视图工具 */}
        {groupedTools.view?.map((tool) => (
          <Tooltip
            key={tool.key}
            title={`${tool.title} (${tool.shortcut})`}
            placement="right"
          >
            <div
              className={`tool-button ${tool.active || activeTool === tool.key ? 'active' : ''}`}
              onClick={() => handleToolClick(tool.key)}
            >
              {tool.icon}
            </div>
          </Tooltip>
        ))}

        {/* 分隔线 */}
        <div className="toolbar-divider" />

        {/* 创建工具 */}
        {groupedTools.create?.map((tool) => (
          <Tooltip
            key={tool.key}
            title={`${tool.title} (${tool.shortcut})`}
            placement="right"
          >
            <div
              className={`tool-button ${tool.active || activeTool === tool.key ? 'active' : ''}`}
              onClick={() => handleToolClick(tool.key)}
            >
              {tool.icon}
            </div>
          </Tooltip>
        ))}

        {/* 弹性空间 */}
        <div style={{ flex: 1 }} />

        {/* 播放控制 */}
        {groupedTools.playback?.map((tool) => (
          <Tooltip
            key={tool.key}
            title={`${tool.title} (${tool.shortcut})`}
            placement="right"
          >
            <div
              className={`tool-button ${tool.active || activeTool === tool.key ? 'active' : ''}`}
              onClick={() => handleToolClick(tool.key)}
            >
              {tool.icon}
            </div>
          </Tooltip>
        ))}

        {/* 设置 */}
        {groupedTools.settings?.map((tool) => (
          <Tooltip
            key={tool.key}
            title={`${tool.title} (${tool.shortcut})`}
            placement="right"
          >
            <div
              className={`tool-button ${tool.active || activeTool === tool.key ? 'active' : ''}`}
              onClick={() => handleToolClick(tool.key)}
            >
              {tool.icon}
            </div>
          </Tooltip>
        ))}
      </div>
    </div>
  );
};

export default LeftToolbar;

